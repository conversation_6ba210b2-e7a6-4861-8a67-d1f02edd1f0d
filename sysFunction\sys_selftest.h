#ifndef __SYS_SELFTEST_H__
#define __SYS_SELFTEST_H__

#include "stdint.h"

// 系统自检结果结构体
typedef struct {
    uint8_t flash_ok;      // Flash检测结果
    uint8_t tf_card_ok;    // TF卡检测结果  
    uint8_t rtc_ok;        // RTC检测结果
    uint32_t flash_id;     // Flash ID
    char rtc_time[32];     // RTC时间字符串
} sys_selftest_result_t;

// 函数声明
void sys_selftest_init(void);                                    // 系统自检初始化
sys_selftest_result_t sys_selftest_run(void);                   // 执行系统自检
void sys_selftest_print_result(sys_selftest_result_t *result);  // 打印自检结果

#endif /* __SYS_SELFTEST_H__ */
