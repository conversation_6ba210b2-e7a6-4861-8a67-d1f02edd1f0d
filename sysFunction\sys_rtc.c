#include "sys_rtc.h"
#include "stdio.h"
#include "main.h"

// 由于项目中没有rtc.h，我们使用系统时间模拟RTC功能
// 如果项目中有真实的RTC硬件，请添加相应的头文件和句柄

static sys_rtc_time_t system_time = {2025, 1, 1, 12, 0, 0}; // 默认系统时间
static uint32_t last_tick = 0; // 上次更新时间的系统tick

void sys_rtc_init(void) {
    // 初始化系统时间模拟RTC
    last_tick = HAL_GetTick(); // 记录初始化时的系统tick
}

uint8_t sys_rtc_set_time(sys_rtc_time_t *time) {
    // 验证时间有效性
    if (time->year < 2000 || time->year > 2099 ||
        time->month < 1 || time->month > 12 ||
        time->day < 1 || time->day > 31 ||
        time->hour > 23 || time->minute > 59 || time->second > 59) {
        return 0; // 时间无效
    }

    // 设置系统时间
    system_time = *time;
    last_tick = HAL_GetTick(); // 更新时间基准

    return 1; // 设置成功
}

uint8_t sys_rtc_get_time(sys_rtc_time_t *time) {
    // 计算时间差
    uint32_t current_tick = HAL_GetTick();
    uint32_t elapsed_ms = current_tick - last_tick;
    uint32_t elapsed_seconds = elapsed_ms / 1000;

    // 更新系统时间
    *time = system_time;

    // 添加经过的秒数
    time->second += elapsed_seconds;

    // 处理进位
    if (time->second >= 60) {
        time->minute += time->second / 60;
        time->second %= 60;
    }
    if (time->minute >= 60) {
        time->hour += time->minute / 60;
        time->minute %= 60;
    }
    if (time->hour >= 24) {
        time->day += time->hour / 24;
        time->hour %= 24;
    }

    // 简化处理：不考虑月份天数差异
    if (time->day > 31) {
        time->month += (time->day - 1) / 31;
        time->day = ((time->day - 1) % 31) + 1;
    }
    if (time->month > 12) {
        time->year += (time->month - 1) / 12;
        time->month = ((time->month - 1) % 12) + 1;
    }

    return 1; // 获取成功
}

uint8_t sys_rtc_parse_time_string(const char *time_str, sys_rtc_time_t *time) {
    // 解析格式: "2025-01-01 12:00:30" 或 "2025-01-01 01:30:10"
    int year, month, day, hour, minute, second;
    
    if (sscanf(time_str, "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second) == 6) {
        // 验证时间有效性
        if (year >= 2000 && year <= 2099 && month >= 1 && month <= 12 && 
            day >= 1 && day <= 31 && hour >= 0 && hour <= 23 && 
            minute >= 0 && minute <= 59 && second >= 0 && second <= 59) {
            
            time->year = year;
            time->month = month;
            time->day = day;
            time->hour = hour;
            time->minute = minute;
            time->second = second;
            return 1; // 解析成功
        }
    }
    
    return 0; // 解析失败
}

void sys_rtc_format_time_string(sys_rtc_time_t *time, char *str) {
    sprintf(str, "%04d-%02d-%02d %02d:%02d:%02d", 
            time->year, time->month, time->day, 
            time->hour, time->minute, time->second); // 格式化时间字符串
}

uint8_t sys_rtc_check_status(void) {
    // 模拟RTC状态检查 - 总是返回正常
    // 如果有真实RTC硬件，这里应该检查RTC寄存器状态
    return 1; // RTC正常
}
